# ssh_gateway/config.py

# --- 服务器端口配置 ---
SSH_PORT = 8022
WEB_PORT = 8002
WEB_HOST = "0.0.0.0"

# --- SSH服务器配置 ---
# 您需要先生成这个密钥文件。命令: ssh-keygen -t rsa -f keys/ssh_host_key
SSH_HOST_KEY_PATH = "keys/ssh_host_key"

# --- 安全配置 ---
# 存放允许连接到此网关的Jumpserver的公钥
# 在Jumpserver系统用户中配置的私钥对应的公钥
# 这可以防止任意SSH客户端连接
ALLOWED_JUMPSERVER_PUBLIC_KEYS = [
    # 示例: "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIHlour/p52T0q2dSp5F4s3W..."
    # 请替换为您自己的Jumpserver公钥
    "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAACAQCeCRjex1q5OF7v4nx6s0bXGU56wR2imb1lar9VdSyEUILyKbzgRxswPALSXNCoH83S9N6mZwNf6p+/4yzxjR5dmdLSMQ8iqcx9gkOyQBi/edQ7WVVj5cRKfgmKOakZWTc+eV7ColGMe6KOPhgD7G1VFS7H4Two9kzjwVg7ae1vElYX92dyBK+0J4VfwIVV+Lt5/cp2grEEY5CLttcMGl1RHpyTAQe62WMQe0u771+l0GIBRKAiEWtLNS8MAMJWI3DvLg+forG9/lDOkKPQ+UHNMmDTlIOO4Hks0OrPOxJF91NFCsD50v9tVTenjQtZB2oUch0aLlaeOQgZdWhqZDc+8U74sA7l0S7/YWn5LpMlKvzbSM34fxd1GaKjE4JqTr9Nyz0UIe8ePbto9ib4fsOhFZ7xd+H444sSRrbS+vvrys5dW6FDGqWzp1UK5+MLCtIsBtkCX9NXzXo8kGuWeJZJcWtRpQr+cU0IocHz75HBWREfVPxo+83ZU0nfgNVZAHihKAo4IG/KWyI/BGHwpJCvszoIt+7i3/qcWxiveh1TIqA+xDjt+Ko32dnLCHaIE6X7VUHMGspcd3FdwX72o95esudxV4LbhYwbNX5Z/KW/EaHhj++IRhG1lQ5HSQGeDrwtVXKcZ0sVNEDURP88EQ4N+Xpv7nbunf5dCSQr+GRngQ== chen@ChenBook"
    "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQC0dW8phGV+a+eT4dKfAPXNA635KU5RPP4sYAsYyKYDIjEBm8an8MoLVZxnX6WWhiA2fzKybOfBG6EjJ3LklX/kFOVzwlhSSrO5FWaalFcYqK8znQItFVKw+9qpyD0u/KX/g0p1p5NmjBVzfF1sbNukC0BSewOE0IuhKDNRQ4aXpC2mbangu7PHBuUVNd97zBCgTdk2EdbjlMmBgcy1GMjrh8cpa2UZMAl8+L2iRxzOrbsxSWMOWwGoAJTkanCVNi9QTve9vIAAQ33Xlb0k0mJ+emj7SgXaydnpj32dpZ1nkQy038kue8H646LfmgPH6tJ0v7tVcFEIRyQsv7AtJtW2A8AHG6E4KRG5bnte8bQLOm1ESqi6LPgWI5K8FvU9zR7J/XDPKnLebRJixB4NuyRIFHPlfUC0BjPTi221kdS9mFG/m2sYi7BFx6/pEr/vKD57UGoV4a23F6VFErWwXT/s4TUKZRkwQ2MOy4SWuSM7JD29J1ujQVS/zFeVGXqcpSE= chen@ChenBook"
]
