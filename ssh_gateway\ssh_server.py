# ssh_gateway/ssh_server.py

import asyncio
import asyncssh
import logging
from .ws_manager import manager
from . import config

class MySSHServerSession(asyncssh.SSHServerSession):
    def __init__(self, device_id: str):
        self._device_id = device_id
        self._chan = None
        super().__init__()

    def connection_made(self, chan: asyncssh.SSHServerChannel):
        """当SSH会话通道建立时调用。"""
        self._chan = chan
        logging.info(f"SSH session created for device '{self._device_id}'.")

    def data_received(self, data: str, datatype: asyncssh.DataType):
        """
        接收来自Jumpserver终端的输入流。
        优化点：直接转发所有数据，以支持交互式应用(vim, top等)，而不仅仅是简单命令。
        """
        # 使用asyncio.create_task在后台发送命令并处理结果，避免阻塞data_received
        asyncio.create_task(self.run_command(data))

    async def run_command(self, command: str):
        """将命令通过WebSocket管理器发送，并等待结果写回SSH通道。"""
        try:
            logging.info(f"SSH -> WS ({self._device_id}): {command!r}")
            # 调用ws_manager发送命令并等待结果
            result = await manager.send_command(self._device_id, command)
            logging.info(f"WS -> SSH ({self._device_id}): {result!r}")
            # 将结果写回给Jumpserver的SSH通道
            self._chan.write(result)
        except ConnectionError as e:
            self._chan.write(f"\nError: {e}\n")
            self._chan.exit(1)
        except asyncio.TimeoutError:
            self._chan.write("\nError: Command timed out.\n")
        except Exception as e:
            logging.error(f"Error processing command for '{self._device_id}': {e}", exc_info=True)
            self._chan.write(f"\nAn internal error occurred: {e}\n")
            self._chan.exit(1)

    def shell_requested(self) -> bool:
        """当客户端请求一个交互式shell时调用。"""
        logging.info(f"Shell requested for device '{self._device_id}'. Accepting.")
        # 接受shell请求，即使我们不提供一个完整的shell，
        # 这对于保持连接至关重要。
        return True

    def pty_requested(self, term: str, term_width: int, term_height: int, term_pix_width: int, term_pix_height: int, modes: bytes) -> bool:
        """当客户端请求伪终端时调用。"""
        logging.info(f"PTY requested for device '{self._device_id}'. Accepting.")
        # 接受PTY请求
        return True
        
    def connection_lost(self, exc: Exception | None) -> None:
        logging.info(f"SSH session for device '{self._device_id}' closed.")


class MySSHServer(asyncssh.SSHServer):
    def session_requested(self) -> MySSHServerSession:
        """当客户端请求会话时，创建我们的自定义会话实例。"""
        device_id = self.get_extra_info('username')
        return MySSHServerSession(device_id)

    def begin_auth(self, username: str) -> bool:
        """
        在密码或公钥认证前调用。
        可以在这里做一些初步的用户名检查。
        """
        # 简单地允许任何用户名，具体的路由逻辑在 session_requested 中处理
        return True # or False

    def public_key_auth_supported(self):
        """声明我们支持公钥认证。"""
        return True

    def validate_public_key(self, username: str, key: asyncssh.public_key) -> bool:
        """
        验证Jumpserver提供的公钥是否在我们允许的列表中。
        """
        key_str = key.export(format='openssh')
        logging.info(f"Attempting auth for user '{username}' with public key: {key_str}")
        
        if config.ALLOWED_JUMPSERVER_PUBLIC_KEYS and key_str not in config.ALLOWED_JUMPSERVER_PUBLIC_KEYS:
            logging.warning(f"Auth failed for '{username}': Public key not in allowed list.")
            return False
            
        logging.info(f"Public key for '{username}' validated successfully.")
        return True
